/**
 * Consent Controller
 *
 * This controller handles consent management operations.
 * It provides endpoints for listing, viewing, approving, denying, and revoking consents.
 *
 * Official ABDM Documentation:
 * https://kiranma72.github.io/abdm-docs/5-building-a-phr-app/managing-consents/index.html
 */

const { User } = require('../../models/user.model');
const { handleError } = require('../../utils/errorUtils');
const consentUtils = require('../utils/consentUtils');
const { sendNotification } = require('../../utils/websocketUtils');
const abhaUtils = require('../../utils/abhaUtils');
const { onResult, onError } = abhaUtils;


write a script that create a new folder with a random name then scan all files in the workspace then create a copy of those file to the 
/**
 * Revoke a consent
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with revocation result
 */
const revokeConsent = async (req, res) => {
  try {
    const userId = req.user.id;
    const { reason, consentId } = req.body;

    if (!reason) {
      return res.status(400).json({ message: 'Reason is required' });
    }
    // Revoke consent in ABDM - pass userId for transaction tracking
    await consentUtils.revokeConsent(consentId, reason, userId);

    return res.status(200).json(onResult('Consent revoked successfully', {
      consentId: consentId
    }));
  } catch (error) {
    return handleError(res, error, 'Failed to revoke consent');
  }
};

/**
 * Create a consent PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with PIN creation result
 */
const createConsentPin = async (req, res) => {
  try {
    const userId = req.user.id;
    const { pin } = req.body;

    if (!pin) {
      return res.status(400).json({ message: 'PIN is required' });
    }

    // Validate PIN format (4-digit numeric) as per ABDM requirements
    if (!/^\d{4}$/.test(pin)) {
      return res.status(400).json({ message: 'PIN must be a 4-digit number' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get preferred ABHA address and validate current profile
    const abhaAddress = user.preferredAbhaAddress;
    if (!abhaAddress) {
      return res.status(400).json({ message: 'No preferred ABHA address found. Please set a current profile.' });
    }

    // Find current profile
    const currentProfile = user.profiles.find(profile => profile.abhaAddress === abhaAddress);
    if (!currentProfile) {
      return res.status(400).json({ message: 'Current profile not found. Please ensure your preferred ABHA address is valid.' });
    }

    // Create PIN using the utility function that calls ABDM API
    await consentUtils.createConsentPin(userId, pin);

    return res.status(200).json(onResult('Consent PIN created successfully', {}));
  } catch (error) {
    return handleError(res, error, 'Failed to create consent PIN');
  }
};

/**
 * Verify a consent PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with verification result
 */
const verifyConsentPin = async (req, res) => {
  try {
    const userId = req.user.id;
    const { pin, requestId, scope } = req.body;

    if (!pin) {
      return res.status(400).json({ message: 'PIN is required' });
    }

    if (!requestId) {
      return res.status(400).json({ message: 'requestId is required' });
    }

    if (!scope) {
      return res.status(400).json({ message: 'scope is required' });
    }

    // Verify PIN using the utility function that calls ABDM API
    const result = await consentUtils.verifyConsentPin(userId, pin, requestId, scope);

    if (!result.success) {
      return res.status(400).json({ message: result.message });
    }

    return res.status(200).json(onResult('PIN verified successfully', {
      temporaryToken: result.temporaryToken
    }));
  } catch (error) {
    return handleError(res, error, 'Failed to verify consent PIN');
  }
};

/**
 * Change a consent PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with PIN change result
 */
const changeConsentPin = async (req, res) => {
  try {
    const userId = req.user.id;
    const { oldPin, newPin } = req.body;

    if (!oldPin || !newPin) {
      return res.status(400).json({ message: 'Old PIN and new PIN are required' });
    }

    // Validate PIN format (4-digit numeric) as per ABDM requirements
    if (!/^\d{4}$/.test(newPin)) {
      return res.status(400).json({ message: 'PIN must be a 4-digit number' });
    }

    // Find user to get ABHA address
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get preferred ABHA address and validate current profile
    const abhaAddress = user.preferredAbhaAddress;
    if (!abhaAddress) {
      return res.status(400).json({ message: 'No preferred ABHA address found. Please set a current profile.' });
    }

    // Find current profile
    const currentProfile = user.profiles.find(profile => profile.abhaAddress === abhaAddress);
    if (!currentProfile) {
      return res.status(400).json({ message: 'Current profile not found. Please ensure your preferred ABHA address is valid.' });
    }

    // Change PIN using the utility function that calls ABDM API
    const result = await consentUtils.changeConsentPin(userId, oldPin, newPin);

    if (!result.success) {
      return res.status(400).json({ message: result.message });
    }

    return res.status(200).json(onResult('Consent PIN changed successfully', {}));
  } catch (error) {
    return handleError(res, error, 'Failed to change consent PIN');
  }
};

/**
 * Generate OTP for forgot PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with OTP generation result
 */
const generateForgotPinOtp = async (req, res) => {
  try {
    const userId = req.user.id;

    // Generate OTP
    const result = await consentUtils.generateForgotPinOtp(userId);

    if (!result.success) {
      return res.status(400).json({ message: result.message });
    }

    return res.status(200).json(onResult('OTP generated successfully', {
      sessionId: result.sessionId
    }));
  } catch (error) {
    return handleError(res, error, 'Failed to generate forgot PIN OTP');
  }
};

/**
 * Validate OTP for forgot PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with OTP validation result
 */
const validateForgotPinOtp = async (req, res) => {
  try {
    const userId = req.user.id;
    const { sessionId, otp } = req.body;

    if (!sessionId || !otp) {
      return res.status(400).json({ message: 'Session ID and OTP are required' });
    }

    // Validate OTP
    const result = await consentUtils.validateForgotPinOtp(userId, sessionId, otp);

    if (!result.success) {
      return res.status(400).json({ message: result.message });
    }

    return res.status(200).json(onResult('OTP validated successfully', {
      temporaryToken: result.temporaryToken
    }));
  } catch (error) {
    return handleError(res, error, 'Failed to validate forgot PIN OTP');
  }
};

/**
 * Reset consent PIN
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with PIN reset result
 */
const resetConsentPin = async (req, res) => {
  try {
    const userId = req.user.id;
    const { temporaryToken, pin } = req.body;

    if (!temporaryToken || !pin) {
      return res.status(400).json({ message: 'Temporary token and PIN are required' });
    }

    // Validate PIN format (4-digit numeric)
    if (!/^\d{4}$/.test(pin)) {
      return res.status(400).json({ message: 'PIN must be a 4-digit number' });
    }

    // Reset PIN
    const result = await consentUtils.resetConsentPin(userId, temporaryToken, pin);

    if (!result.success) {
      return res.status(400).json({ message: result.message });
    }

    return res.status(200).json(onResult('Consent PIN reset successfully', {}));
  } catch (error) {
    return handleError(res, error, 'Failed to reset consent PIN');
  }
};

/**
 * Check consent PIN status for current profile
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with PIN status
 */
const checkConsentPinStatus = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check PIN status
    const result = await consentUtils.checkConsentPinStatus(userId);

    return res.status(200).json(onResult('Consent PIN status retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to check consent PIN status');
  }
};



/**
 * Handle consent request callback
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with acknowledgement
 *
 * Request Body:
 * {
 *   "consentRequest": {
 *     "id": "05f14b1d-4465-453a-8249-1382d79d271d"
 *   },
 *   "error": null,
 *   "response": {
 *     "requestId": "4213ebf8-5f8a-45e4-a014-7a2eb875f213"
 *   }
 * }
 */
const handleConsentRequestOnInitCallback = async (req, res) => {
  try {
    console.log('Received consent request callback:', req.body);

    // Always respond with 202 to acknowledge receipt
    res.status(202).send();

    // Extract consent request details
    const { consentRequest, error, response } = req.body;

    if (error) {
      console.error('Error in consent request callback:', error);
      return;
    }


    console.log(`Updated transaction record for request ID: ${response.requestId}`);
    await consentUtils.getConsentRequestStatus(consentRequest.id, response.requestId);
  } catch (error) {
    console.error('Error processing consent request callback:', error);
    // Still return 202 to acknowledge receipt
    if (!res.headersSent) {
      res.status(202).send();
    }
  }
};

/**
 * Initialize a consent request
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with initialization result
 */
const initializeConsentRequest = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      patientId,
      purpose,
      purposeCode,
      hiTypes,
      accessMode,
      dateRange,
      dataEraseAt,
      frequency,
      requesterName,
      requesterIdentifier
    } = req.body;

    // Validate required fields
    if (!patientId || !purpose) {
      return res.status(400).json({ message: 'Patient ID and purpose are required' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prepare consent details
    const consentDetail = {
      purpose,
      purposeCode,
      hiTypes,
      accessMode,
      dateRange,
      dataEraseAt,
      frequency,
      requesterName,
      requesterIdentifier
    };

    // Initialize consent request and create transaction in the utility function
    await consentUtils.initializeConsentRequest(patientId, consentDetail, userId);

    return res.status(200).json(onResult('Consent request initialized successfully', {}));
  } catch (error) {
    return handleError(res, error, 'Failed to initialize consent request');
  }
};

/**
 * Handle consent fetch callback
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with acknowledgement
 */
const handleConsentFetchCallback = async (req, res) => {
  try {
    console.log('Received consent fetch callback:', req.body);

    // Always respond with 202 to acknowledge receipt
    res.status(202).send();

    // Process callback asynchronously
    try {
      consentUtils.processConsentOnFetchCallback(req.body).catch(error => {
        console.error('Error processing consent fetch callback:', error);
      });

    } catch (error) {
      console.error('Error in consent fetch callback processing:', error);
    }
  } catch (error) {
    console.error('Error in consent fetch callback:', error);
    // Still return 202 to acknowledge receipt
    if (!res.headersSent) {
      res.status(202).send();
    }
  }
};

/**
 * Handle consent request status callback
 * This function processes the status update for a consent request.
 * It acknowledges the receipt of the callback and then processes it asynchronously.
 *
 * @param {Object} req - The request object containing the callback data
 * @param {Object} res - The response object used to send the acknowledgement
 */
const handleConsentRequestStatusCallback = async (req, res) => {
  try {
    console.log('Received consent request status callback:', req.body);

    // Always respond with 202 to acknowledge receipt
    res.status(202).send();

    // Process callback asynchronously
    const { consentRequest, error, response } = req.body;
    await consentUtils.handleConsentRequestOnStatusCallback(consentRequest, error, response.requestId);


  } catch (error) {
    console.error('Error in consent request status callback:', error);
    // Still return 202 to acknowledge receipt
    if (!res.headersSent) {
      res.status(202).send();
    }
  }
};

/**
 * Approve a consent request
 * This function allows a user to approve a consent request with specific parameters.
 * It accepts a list of consent IDs and fetches the consent details directly from ABDM API.
 * Consent data structure:
 * "consents": [
 *     {
 *         "hiTypes": [
 *             "Prescription",
 *             "DiagnosticReport",
 *             "OPConsultation",
 *             "DischargeSummary",
 *             "ImmunizationRecord",
 *             "HealthDocumentRecord",
 *             "WellnessRecord",
 *             "Invoice"
 *         ],
 *         "hip": {
 *             "id": "{{hip-id}}"
 *         },
 *         "careContexts": [
 *             {
 *                 "patientReference": "3b07a072-a2d4-471e-9a5a-d5501a7f34bd",
 *             "careContextReference": "DFCConnect_2024-04-01-02514"
 *             }
 *         ],
 *         "permission": {
 *             "dateRange": {
 *                 "to": "2025-02-24T19:20:00.000Z",
 *                 "from": "2024-01-01T05:20:00.000Z"
 *             },
 *             "frequency": {
 *                 "unit": "DAY",
 *                 "value": 0,
 *                 "repeats": 0
 *             },
 *             "accessMode": "VIEW",
 *             "dataEraseAt": "2025-09-31T07:00:00.000Z"
 *         }
 *     }
 * ]
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with approval result
 */
const approveConsentRequest = async (req, res) => {
  try {
    const userId = req.user.id;
    const { consents, consentRequestId } = req.body;

    if (!consentRequestId) {
      return res.status(400).json({ message: 'Consent request ID is required' });
    }

    if (!consents || !Array.isArray(consents) || consents.length === 0) {
      return res.status(400).json({ message: 'Consents array is required and must not be empty' });
    }

    // Validate each consent object
    for (const consent of consents) {
      if (!consent.hiTypes || !Array.isArray(consent.hiTypes) || consent.hiTypes.length === 0) {
        return res.status(400).json({ message: 'hiTypes array is required for each consent and must not be empty' });
      }
      if (!consent.hip || !consent.hip.id) {
        return res.status(400).json({ message: 'hip object with id is required for each consent' });
      }
      if (!consent.careContexts || !Array.isArray(consent.careContexts) || consent.careContexts.length === 0) {
        return res.status(400).json({ message: 'careContexts array is required for each consent and must not be empty' });
      }
      if (!consent.permission || !consent.permission.dateRange || !consent.permission.frequency || !consent.permission.accessMode || !consent.permission.dataEraseAt) {
        return res.status(400).json({ message: 'permission object with dateRange, frequency, accessMode, and dataEraseAt is required for each consent' });
      }
    }


    // Call utility function to approve the consent request
    await consentUtils.approveConsentRequest(userId, consents, consentRequestId);

    return res.status(200).json(onResult('Consent request approved successfully', consentRequestId));

  } catch (error) {
    return handleError(res, error, 'Failed to approve consent request');
  }
};



/**
 * Deny a consent request
 * This function allows a user to deny a consent request with a specific reason.
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with denial result
 */
const denyConsentRequest = async (req, res) => {
  try {
    const userId = req.user.id;
    const { reason, consentRequestId } = req.body;

    if (!consentRequestId) {
      return res.status(400).json({ message: 'Consent request ID is required' });
    }

    if (!reason) {
      return res.status(400).json({ message: 'Reason for denial is required' });
    }



    // Call utility function to deny the consent request
    const result = await consentUtils.denyConsentRequest(userId, consentRequestId, reason);

    return res.status(200).json({
      message: 'Consent request denied successfully',
      consentRequestId
    });
  } catch (error) {
    return handleError(res, error, 'Failed to deny consent request');
  }
};

/**
 * List consent requests for a user
 * This function retrieves all consent requests for the authenticated user.
 * It can filter by status and supports pagination.
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with list of consent requests
 */
const listConsentRequests = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit, offset, status } = req.query;

    // Convert limit and offset to numbers if provided
    const limitNum = limit ? parseInt(limit, 10) : 100;
    const offsetNum = offset ? parseInt(offset, 10) : 0;

    // Call utility function to list consent requests and update database records
    const result = await consentUtils.listConsentRequests(
      userId,
      limitNum,
      offsetNum,
      status
    );



    return res.status(200).json(onResult('Consent requests retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to list consent requests');
  }
};

/**
 * Fetch consent details from ABDM
 * This function initiates a fetch of consent details from ABDM.
 * The actual data will be received in the callback and processed separately.
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with fetch initiation result
 */
const getConsentDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { consentId } = req.params;


    // Initiate fetch from ABDM
    const result = await consentUtils.fetchConsentDetails(userId, consentId);

    return res.status(200).json({
      message: 'Consent fetch initiated successfully',
      consentId: consentId,
      requestId: result.requestId
    });
  } catch (error) {
    return handleError(res, error, 'Failed to fetch consent details');
  }
};

/**
 * Handle consent request notify callback from HIE-CM to HIU
 * This endpoint is called by HIE-CM when a consent request is approved, revoked, or denied.
 * Notification structure:
 * {
 *  "notification": {
 *    "consentRequestId": "e3c74829-3f82-4f94-959e-e10f57bcd57b",
 *    "status": "GRANTED",
 *    "reason": null,
 *    "consentArtefacts": [
 *      {
 *        "id": "<consent-artefact-id>"
 *      }
 *    ]
 *  }
 * }
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with acknowledgement
 */
const handleConsentRequestNotifyCallback = async (req, res) => {
  console.log('Received consent request notify callback:', req.body);
  const { notification } = req.body;
  try {
    console.log('Notification:', notification);
    // Always respond with 202 to acknowledge receipt
    res.status(202).send();
    // Extract data from request based on ABDM documentation format
    // The notification is wrapped in a notification object
    const { status, consentRequestId, consentArtefacts, reason } = notification;

    if (consentArtefacts) {
      await consentUtils.handleConsentRequestNotification(status, consentRequestId, consentArtefacts, reason);
      // Acknowledge the notification
      await consentUtils.acknowledgeConsentNotification(consentArtefacts, consentRequestId);
    }

  } catch (error) {
    // Still return 202 to acknowledge receipt if headers not sent yet
    if (!res.headersSent) {
      res.status(202).send();
    }
    // Try to acknowledge with error
    if (req.body && req.body.notification) {
      const { consentArtefacts, consentRequestId } = req.body.notification;
      return await consentUtils.acknowledgeConsentNotification(consentArtefacts, consentRequestId, error);
    }
    console.log("error handling consent notification", error)

  }
};

/**
 * Set up auto-approve consent for a user
 * This function calls the ABDM API to set up auto-approve consent settings
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with auto-approve setup result
 */
const consentAutoApprove = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user to verify they exist
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Call utility function to set up auto-approve consent
    const result = await consentUtils.consentAutoApprove(userId);

    return res.status(200).json(onResult('Consent auto-approve set up successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to set up consent auto-approve');
  }
};

/**
 * Toggle auto-approve consent for a specific consent ID
 * This function enables or disables auto-approve for a specific consent
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with toggle result
 */
const toggleConsentAutoApprove = async (req, res) => {
  try {
    const userId = req.user.id;
    const { consentId } = req.params;
    const { enable } = req.body;

    if (enable === undefined) {
      return res.status(400).json({ message: 'Enable flag is required (true/false)' });
    }
    // Call utility function to toggle auto-approve
    const result = await consentUtils.toggleConsentAutoApprove(consentId, enable, userId);

    return res.status(200).json(onResult(`Consent auto-approve ${enable ? 'enabled' : 'disabled'} successfully`, result));
  } catch (error) {
    return handleError(res, error, 'Failed to toggle consent auto-approve');
  }
};

/**
 * Get consent request details by request ID
 * This function retrieves detailed information about a specific consent request
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with consent request details
 */
const getConsentRequestDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { requestId } = req.params;

    if (!requestId) {
      return res.status(400).json({ message: 'Consent request ID is required' });
    }

    // Call utility function to get consent request details
    const result = await consentUtils.getConsentRequestDetails(userId, requestId);

    return res.status(200).json(onResult('Consent request details retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to get consent request details');
  }
};

/**
 * Get all consent artifact details by request ID
 * This function retrieves all consent artifacts associated with a specific request
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with consent artifacts
 */
const getConsentArtifactsByRequestId = async (req, res) => {
  try {
    const userId = req.user.id;
    const { requestId } = req.params;

    if (!requestId) {
      return res.status(400).json({ message: 'Consent request ID is required' });
    }

    // Call utility function to get consent artifacts by request ID
    const result = await consentUtils.getAllConsentArtifactDetailsByRequestId(userId, requestId);

    return res.status(200).json(onResult('Consent artifacts retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to get consent artifacts');
  }
};

/**
 * Get consent artifact details by artifact ID
 * This function retrieves detailed information about a specific consent artifact
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with consent artifact details
 */
const getConsentArtifactById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { artifactId } = req.params;

    if (!artifactId) {
      return res.status(400).json({ message: 'Consent artifact ID is required' });
    }

    // Call utility function to get consent artifact by ID
    const result = await consentUtils.getAllConsentArtifactDetailsByArtefactId(userId, artifactId);

    return res.status(200).json(onResult('Consent artifact details retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to get consent artifact details');
  }
};

/**
 * Get all consent artifacts for a user's ABHA address
 * This function retrieves all consent artifacts associated with the user's ABHA address
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with consent artifacts
 */
const getConsentArtifactsByAbhaAddress = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit, offset, status } = req.query;

    // Convert limit and offset to numbers if provided
    const limitNum = limit ? parseInt(limit, 10) : 100;
    const offsetNum = offset ? parseInt(offset, 10) : 0;

    // Call utility function to get consent artifacts by ABHA address
    const result = await consentUtils.getAllConsentArtifactDetailsByAbhaAddress(userId, limitNum, offsetNum, status);

    return res.status(200).json(onResult('Consent artifacts retrieved successfully', result));
  } catch (error) {
    return handleError(res, error, 'Failed to get consent artifacts');
  }
};

module.exports = {



  createConsentPin,
  verifyConsentPin,
  changeConsentPin,
  generateForgotPinOtp,
  validateForgotPinOtp,
  resetConsentPin,
  checkConsentPinStatus,


  //initialize consent request
  initializeConsentRequest,
  //handle consent request callback to check for consent request status
  handleConsentRequestOnInitCallback,
  //check consent request status and notify frontend to refresh consent request list
  handleConsentRequestStatusCallback,
  //handle consent request notify callback from HIE-CM to HIU such as requested, granted, revoked. this will call fetch consent details
  handleConsentRequestNotifyCallback,


  // get consent details from ABDM. Callback will be handled by handleConsentFetchCallback
  getConsentDetails,
  //handle consent fetch callback to update our database and notify frontend to with updated consent details handleConsentFetchCallback,
  handleConsentFetchCallback,

  approveConsentRequest,
  denyConsentRequest,
  revokeConsent,
  listConsentRequests,

  // auto-approve consent management
  consentAutoApprove,
  toggleConsentAutoApprove,


  // detailed consent information retrieval
  getConsentRequestDetails,
  getConsentArtifactsByRequestId,
  getConsentArtifactById,
  getConsentArtifactsByAbhaAddress
};
